import React from 'react';
import { PersonIcon, PeopleOutlineIcon } from '~/components/Icon';
import {
  HKChecklistOption,
  HKExtendedJob,
  HKPerformType,
  HKSection,
  HKTask,
} from '~types';

export const dummyItems: HKTask[] = [
  {
    id: '84',
    name: 'Call the house phone number to inform the guests that you are on your way.',
    section_id: '70',
    order: '1',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '108',
    name: `Make sure you have the supplies below

Guest cleaning supplies (in Goodie Box)
- Disposable paper towels 2 rolls
- Lysol Multi-surface cleaner
- Clorox Disinfectant wipes or Lysol Disinfectant spray
- Antibacterial hand sanitizer
- Extra hand soap
- Dishwasher pods 6
- Laundry detergent pods 6
- Doggy waste bags
- Dog training pads 3
- Dog urine cleanup spray
- Insect spray`,
    section_id: '70',
    order: '1',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '85',
    name: 'Message in the group chat to advise that you’re on your way.',
    section_id: '70',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '92',
    name: `Ensure you have the following refill supplies:

- Body wash, shampoo, and conditioner
- Toilet paper
- Makeup remover wipes`,
    section_id: '70',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },

  {
    id: '110',
    name: 'Make sure you have the supplies below\n\nProducts\n- Multi surface cleaner\n- Bleach free Multi surface disinfectant\n- Glass cleaner\n- Bleach\n- Laundry detergent\n- Laundry stain remover\n- Dishwashing detergent\n- Carpet cleaner\n- Floor cleaner\n- Furniture/wood polish\n- Oven cleaner\n- Oven degreaser\n- Mold cleaner\n- Magic eraser\n- Swiffer duster\n- CLR\n- Air wick\n- Garbage bags\n- Paper towel\n- Shampoo Refill\n- Body wash Refill\n- Conditioner Refill\n- Hand soap Refill\n- Dish soap Refill',
    section_id: '70',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '107',
    name: 'Contact your helper to make sure they’re on their way.',
    section_id: '70',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '112',
    name: 'Make sure you have the supplies below\n\nProtective gear\n- Disposable gloves\n- Mask or cloth facial coverings\n- Safety glasses\n- Apron or gown\n- Hairnet\n- Shoe Coverings',
    section_id: '70',
    order: '3',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '109',
    name: 'Make sure you have the supplies below\r\n\r\n- Broom\r\n- Bucket\r\n- Duster\r\n- Extendable Dustpan\r\n- Microfiber cloths\r\n- Scrub pads\r\n- Scrub brush\r\n- Step ladder\r\n- Toilet brush\r\n- Vacuum cleaner\r\n- Mop\r\n- Flashlight\r\n- Lint roller',
    section_id: '70',
    order: '4',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '111',
    name: 'Make sure you have the supplies below\n\nProtective gear\n- Disposable gloves\n- Mask or cloth facial coverings\n- Safety glasses\n- Apron or gown\n- Hairnet\n- Shoe Coverings',
    section_id: '70',
    order: '5',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '113',
    name: 'Make sure you have the supplies below\r\n\r\n- Welcome Tray\r\n- Water\r\n- Wine\r\n- Chocolate\r\n- Candy\r\n- Chips or popcorn',
    section_id: '70',
    order: '6',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '87',
    name: 'Open the garage door with keypad, and if there are two, open both to ensure they operate correctly and close automatically.',
    section_id: '71',
    order: '1',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '88',
    name: 'Park your car in the garage.',
    section_id: '71',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '123',
    name: 'Verify that the garage doors are self-closing after 1 minute.',
    section_id: '71',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '114',
    name: 'Put on gloves and hairnet.',
    section_id: '71',
    order: '3',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '116',
    name: 'Open the patio door and front door or windows fully for 10 minutes to air the house.',
    section_id: '71',
    order: '4',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '125',
    name: 'Ensure that at least three garage remotes are available, securely attached to a cutting board, and hanging on a hook rack. Test each remote to confirm they all function properly.',
    section_id: '71',
    order: '4',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '117',
    name: 'Record a brief video of the house to document its current condition, highlighting any visible damages or areas with excessive dirt.',
    section_id: '71',
    order: '5',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '126',
    name: 'Verify that all the locks are working and self-closing.',
    section_id: '71',
    order: '5',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '118',
    name: 'Turn on all the lights and fans while recording the video, ensuring they are functioning properly, and leave them on after testing.',
    section_id: '71',
    order: '6',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '127',
    name: 'Verify that the water heater is set to medium (B).',
    section_id: '71',
    order: '6',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '119',
    name: 'Send a message to the group chat to report any missing items or damages.',
    section_id: '71',
    order: '7',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '128',
    name: 'Check that the water softener is functioning properly, ensure it is filled with salt, and confirm there are no error messages displayed.',
    section_id: '71',
    order: '7',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '120',
    name: 'Verify vents are clean.',
    section_id: '71',
    order: '8',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '121',
    name: 'Put a MINUT device on the charger. It needs a USB-C cord. Charge a different device every time.',
    section_id: '71',
    order: '9',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '122',
    name: 'Set AC no lower than 72 and no higher than 78. Clean the thermostat.',
    section_id: '71',
    order: '10',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '129',
    name: 'Inspect bedding and spray spot cleaner on stains before taking them off!\r\n\r\nTake off the bedding from every single bed, even if it’s not used.\r\n\r\nInspect curtains and drapes and remove for laundry as needed. \r\n\r\nGrab all the towels, bathmats, pool towels, throw pillow covers and blankets and take them to the laundry room for washing. \r\n',
    section_id: '72',
    order: '1',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '169',
    name: 'Pick up all the trash around the entire house.',
    section_id: '72',
    order: '1',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '130',
    name: 'Start the laundry by loading the washer to a maximum of ¾ capacity and the dryer to a maximum of ½ capacity. Avoid overloading, as it can lead to poor wash quality, longer drying times, and potential damage to the equipment.',
    section_id: '72',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '170',
    name: 'Refill hand soap.\r\nRefill shower dispensers with Body Wash, Shampoo and Conditioner\r\nRefill toilet paper holders and leave 3 extra rolls per bathroom. \r\nRefill Makeup remover wipes.',
    section_id: '72',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '131',
    name: 'Start cleaning the bedrooms one by one.',
    section_id: '72',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '171',
    name: 'Start with the Bathrooms one by one.',
    section_id: '72',
    order: '3',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '132',
    name: 'Dust off ceiling fans.',
    section_id: '72',
    order: '4',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '172',
    name: 'Vacuum the floor thoroughly, ensuring that all hair is picked up.',
    section_id: '72',
    order: '4',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '133',
    name: 'Dust off pictures/frames, with the swiffer duster.',
    section_id: '72',
    order: '5',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '173',
    name: 'Check that all lightbulbs are the same color and intensity, and confirm that they are working properly.',
    section_id: '72',
    order: '5',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '134',
    name: 'Check that the window sills and windows are clean and free of dirt or smudges.',
    section_id: '72',
    order: '6',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '174',
    name: 'Wipe down the light switches to remove any dust or grime. Use Magic Eraser. ',
    section_id: '72',
    order: '6',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '135',
    name: 'Check under the bed, nightstand, and dressers to ensure all trash is picked up.',
    section_id: '72',
    order: '7',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '136',
    name: 'Use a blower to clean under the bed and nightstand. If there is a rug, make sure it is clean as well.',
    section_id: '72',
    order: '8',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '176',
    name: 'Clean the sink, around the sink, and the faucets.',
    section_id: '72',
    order: '8',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '137',
    name: 'Clean the mirrors.',
    section_id: '72',
    order: '9',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '177',
    name: 'Wipe down the countertops, cabinets, handles and decorative pieces.',
    section_id: '72',
    order: '9',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '138',
    name: 'Wipe down the cabinets, nightstands (top to bottom), and all surfaces. Use furniture polish as needed for a clean finish.',
    section_id: '72',
    order: '10',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '178',
    name: 'Open the cabinets to verify it is clean and remove anything that does not belong in there.',
    section_id: '72',
    order: '10',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '139',
    name: 'Wipe off electronics.',
    section_id: '72',
    order: '11',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '179',
    name: 'Clean the shower, including the walls, shower head, tub, and faucets. Remove any calcium deposits as needed.',
    section_id: '72',
    order: '11',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '140',
    name: 'Clean light switches.',
    section_id: '72',
    order: '12',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '180',
    name: 'Clean the glass shower doors, make sure it is streak free.',
    section_id: '72',
    order: '12',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '141',
    name: 'Clean the doorknobs using a Magic Eraser to remove any dirt or grime.',
    section_id: '72',
    order: '13',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '181',
    name: 'Clean the shower curtains as needed, and be sure to wash them at least once a month.',
    section_id: '72',
    order: '13',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '142',
    name: 'Ensure the closet is clean and that all hangers are matching.',
    section_id: '72',
    order: '14',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '182',
    name: 'Clean the toilet thoroughly, from top to bottom, inside and out. Be sure to flush the toilet after cleaning.',
    section_id: '72',
    order: '14',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '143',
    name: 'Go around the bedrooms, and when exiting, check the hallways. Use a Magic Eraser to remove any marks from the walls, floors, and baseboards.',
    section_id: '72',
    order: '15',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '183',
    name: 'Clean the walls around the toilet.',
    section_id: '72',
    order: '15',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '144',
    name: 'Verify Hampers are clean and empty.',
    section_id: '72',
    order: '16',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '184',
    name: 'Make sure the toilet brush and the plunger are clean. Replace it every August. Clean the toilet brush canister.',
    section_id: '72',
    order: '16',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '145',
    name: 'Turn on the TV and have Netflix in the background (replace batteries as needed) Verify remote is in the corresponding room.',
    section_id: '72',
    order: '17',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '185',
    name: 'Vacuum and mop the floors, paying close attention to the corners. Make sure there is no hair left on the floor. Use fresh water every 1,000 sq ft or every 3 rooms, and replace the mop head after each cleaning.',
    section_id: '72',
    order: '17',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '146',
    name: 'If the bed has a metal frame, put on clean bed skirts.',
    section_id: '72',
    order: '18',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '186',
    name: 'Start with the kitchen.',
    section_id: '72',
    order: '18',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '147',
    name: 'Verify that the bed is centered with the picture on the wall and with the rugs.',
    section_id: '72',
    order: '19',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '187',
    name: 'Grab all the dirty dishes and start the dishwasher.',
    section_id: '72',
    order: '19',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '148',
    name: 'Ensure that the nightstand is positioned close to the bed, with nightlights centered and the seams facing the wall. Place plants in the back corner of the rooms.',
    section_id: '72',
    order: '20',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '188',
    name: 'Empty fridge and clean as needed.',
    section_id: '72',
    order: '20',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '149',
    name: 'Once all the bedrooms are done: Vacuum and sweep bedroom and hallways and put on shoe covers.',
    section_id: '72',
    order: '21',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '189',
    name: 'Make sure the freezer is empty and it has no ice buildups.',
    section_id: '72',
    order: '21',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '150',
    name: 'Fold the bath towels and set them up nicely on the beds in the bedroom and put the extra towels in the cabinets in the bathrooms.',
    section_id: '72',
    order: '22',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '190',
    name: 'Open the microwave to make sure it is working and clean it as needed.',
    section_id: '72',
    order: '22',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '151',
    name: 'Set hand towels out nicely on the towel racks and the washcloths in the cabinets in each bathroom.',
    section_id: '72',
    order: '23',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '191',
    name: 'Open the oven, clean it as needed.',
    section_id: '72',
    order: '23',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '152',
    name: 'Fill up the pool towel box with the freshly washed neatly folded towels.',
    section_id: '72',
    order: '24',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '192',
    name: 'Clean the stovetop, and the exhaust hood.',
    section_id: '72',
    order: '24',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '153',
    name: 'Go to the living room.',
    section_id: '72',
    order: '25',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '193',
    name: 'Check the Keurig coffee machine and clean it as needed. Rinse with a cleaner pod.',
    section_id: '72',
    order: '25',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '154',
    name: 'Clean off the fans.',
    section_id: '72',
    order: '26',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '194',
    name: 'Check the Drip coffee machine and clean it as needed.',
    section_id: '72',
    order: '26',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '195',
    name: 'Empty and clean the kitchen vacuum.',
    section_id: '72',
    order: '27',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '156',
    name: 'Dust off picture frames, clean mirrors.',
    section_id: '72',
    order: '28',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '196',
    name: 'Open all the cabinets. Make sure the cabinet doors and handles are clean.',
    section_id: '72',
    order: '28',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '157',
    name: 'Wipe down all the surfaces.',
    section_id: '72',
    order: '29',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '197',
    name: 'Organize the cabinets based on the labels throughout the kitchen.',
    section_id: '72',
    order: '29',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '158',
    name: "Clean the sofa thoroughly. If it's velvet, vacuum it. If it's leather, use a leather conditioner cleaner to maintain its condition.",
    section_id: '72',
    order: '30',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '198',
    name: 'Check all utensils to ensure they are clean. \r\n- Wash them as needed and remove any chipped or broken items. \r\n- Verify that there are at least 16 sets of utensils and plate sets. \r\n- Report if any replacements are necessary for purchase.',
    section_id: '72',
    order: '30',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '159',
    name: 'Clean under the sofa by moving it for easier access. Vacuum and mop underneath to ensure it’s thoroughly cleaned.',
    section_id: '72',
    order: '31',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '199',
    name: 'Check all pots and pans to ensure they are clean and in good condition. Wash them as necessary and remove any that are in poor condition. Report if replacements are needed.',
    section_id: '72',
    order: '31',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '160',
    name: 'Turn on the TV to create some ambiance in the background, replacing batteries as needed. Clean the TV remotes to ensure they are in good condition. Use Magic Eraser on remotes. ',
    section_id: '72',
    order: '32',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '200',
    name: 'Check all glasses and wine glasses. For those that are clean, ensure they are free of water marks.',
    section_id: '72',
    order: '32',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '161',
    name: 'Verify throw pillows and blankets are clean and washed. Fluff and neatly set it up.',
    section_id: '72',
    order: '33',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '201',
    name: 'Check all the plates, bowls, mugs to make sure they are clean. Wash as necessary.',
    section_id: '72',
    order: '33',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '162',
    name: 'Spot treat the carpet and rugs as needed. If the carpet or rug appears really dirty, use a carpet cleaner to clean it thoroughly.',
    section_id: '72',
    order: '34',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '202',
    name: 'Wipe down the trash can inside and out.',
    section_id: '72',
    order: '34',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '163',
    name: 'Vacuum and mop the living room. Use fresh water every 1,000 sq ft or every 3 rooms, and replace the mop head after each cleaning.',
    section_id: '72',
    order: '35',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '203',
    name: 'Assist Person #1 with making the beds.',
    section_id: '72',
    order: '35',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '164',
    name: 'After completing the laundry, contact Person #2 to assist with making the beds.\r\n\r\nCheck the bedding for any stains or damage before putting it on the beds.\r\n\r\nPrepare the bed with fresh bedding, ensuring the following items are properly placed:\r\n- Mattress protector\r\n- Fitted sheet\r\n- Flat sheet\r\n- Duvet with duvet cover\r\n- Two pillows (suitable for two people)\r\n\r\nUse lint rollers on the bedding to remove any hair that may have adhered to it.',
    section_id: '72',
    order: '36',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '204',
    name: 'Refill hand soap and dish soap.',
    section_id: '72',
    order: '36',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '165',
    name: 'Set throw pillows and blankets on the bed. Fluff and neatly set it up.',
    section_id: '72',
    order: '37',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '205',
    name: 'Set out a new sponge.',
    section_id: '72',
    order: '37',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '166',
    name: 'Go around the house with a magic eraser and remove marks from the walls, floors, baseboards.',
    section_id: '72',
    order: '38',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '206',
    name: 'Refill napkins and paper towels.',
    section_id: '72',
    order: '38',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '167',
    name: 'Once beds are made, put on a shoe cover then mop the floors.',
    section_id: '72',
    order: '39',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '207',
    name: 'Refill the welcome tray with the following items:  \r\n- 1 bottle of wine  \r\n- 6 bottles of water  \r\n- 4-6 bags of chips or popcorn, depending on the size of the house  \r\n- Candy or chocolate',
    section_id: '72',
    order: '39',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '168',
    name: 'Once all rooms are mopped, place a bathmat in each bathroom. Verify that there is no hair on the mats.',
    section_id: '72',
    order: '40',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '208',
    name: 'Clean out the sink and run the garbage disposal.',
    section_id: '72',
    order: '40',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '209',
    name: 'Wipe down all the surfaces, kitchen island, behind and under small kitchen appliances.',
    section_id: '72',
    order: '41',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '210',
    name: 'Wipe down the dining room table, chairs, decorative items and all the surfaces in the dining room.',
    section_id: '72',
    order: '42',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '211',
    name: "Empty the dishwasher. Clean it as needed. Wipe down the outside if it's stainless steel.",
    section_id: '72',
    order: '43',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '212',
    name: 'Vacuum and mop the kitchen, the dining room and second living room.\n(Use fresh water every 1000 sq ft or every 3 rooms and use a fresh mop head every cleaning.)',
    section_id: '72',
    order: '44',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '213',
    name: 'Remove shoe covers.',
    section_id: '73',
    order: '1',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '214',
    name: 'Verify outdoor lights are working. Turn on bistro or string lights.',
    section_id: '73',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '222',
    name: 'Clean the BBQ.',
    section_id: '73',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '215',
    name: 'Verify patio furniture is clean. Blow off any dirt.',
    section_id: '73',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '223',
    name: 'Turn off the BBQ’s gas valve.',
    section_id: '73',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '216',
    name: 'Hose off the patio. Pressure wash as needed.',
    section_id: '73',
    order: '4',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '224',
    name: 'Go to the garage.',
    section_id: '73',
    order: '4',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '217',
    name: 'Take a picture of the pool, and spa.',
    section_id: '73',
    order: '5',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '225',
    name: 'Verify garage floors are clean. Blow out any dirt / pressure wash if needed.',
    section_id: '73',
    order: '5',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '218',
    name: 'Verify that the in-ground spa heater is OFF. Take a picture of the panel. (Above ground spas must be left on and covered)',
    section_id: '73',
    order: '6',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '226',
    name: 'Organize the garage. Verify it is clutter free.',
    section_id: '73',
    order: '6',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '219',
    name: 'Empty and wipe down the washer and dryer. Remove lint. Make sure nothing is left in the washer and dryer.',
    section_id: '73',
    order: '7',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '227',
    name: 'Blow out the front door area. Pick up any trash. Shake out the front door mat. Clean the doorbell.',
    section_id: '73',
    order: '7',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '220',
    name: 'Clean the (Dyson) vacuum with all its attachments.',
    section_id: '73',
    order: '8',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '228',
    name: 'Check special features, such as the fish pond.',
    section_id: '73',
    order: '8',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '229',
    name: 'Put on shoe covers and walk around with a flashlight, Swiffer Duster and lint roller and fix things as needed.',
    section_id: '74',
    order: '1',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '230',
    name: 'Take a picture of the utility closet, and write down what you need extra for the next cleaning.',
    section_id: '74',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '233',
    name: 'Use Lysol spray to disinfect the air.',
    section_id: '74',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '231',
    name: "Check air wicks around the house to make sure all the rooms have one and it is not empty. Verify it's clean. Set to the lowest level.",
    section_id: '74',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '234',
    name: 'Make sure trash cans are not overflowing and there is ⅓ capacity remaining for new trash. Move excess trash into black yard bags.',
    section_id: '74',
    order: '3',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '235',
    name: 'Verify side gates are locked.',
    section_id: '74',
    order: '4',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '236',
    name: 'Start taking slow good quality videos room by room. In the kitchen open the fridge, freezer, microwave and the oven record the inside of it as well. The bedrooms show under the bed, inside cabinets. Show lint filter cleaned in dryer.',
    section_id: '75',
    order: '1',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '241',
    name: 'Follow the person taking the video and close the cabinets and turn off the lights except the night lights and ambiance lights – leave those on.',
    section_id: '75',
    order: '1',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '237',
    name: 'Take videos outside, trashcans, pools, garage and the front of the house.',
    section_id: '75',
    order: '2',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '242',
    name: 'Close the doggy door.',
    section_id: '75',
    order: '2',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '238',
    name: 'Lock all of the doors. (patio doors, front doors, garage access doors and garage doors.)',
    section_id: '75',
    order: '3',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '243',
    name: 'Compare reference photos to make sure you set everything up correctly.',
    section_id: '75',
    order: '3',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '239',
    name: 'Verify that all the items on the list are completed.',
    section_id: '75',
    order: '4',
    person1: true,
    person2: true,
    all: true,
  },
  {
    id: '240',
    name: 'Send videos to the group chat.',
    section_id: '75',
    order: '5',
    person1: true,
    person2: false,
    all: true,
  },
  {
    id: '245',
    name: 'Walk around one last time.',
    section_id: '75',
    order: '5',
    person1: false,
    person2: true,
    all: true,
  },
  {
    id: '105',
    name: 'Stock up for next cleaning.',
    section_id: '76',
    order: '1',
    person1: true,
    person2: true,
    all: true,
  },
];

export const dummySections: HKSection[] = [
  { id: '70', manager_id: '4919', name: 'Before arrival', order: '1' },
  { id: '71', manager_id: '4919', name: 'Arriving at the house', order: '2' },
  { id: '72', manager_id: '4919', name: 'Cleaning', order: '3' },
  {
    id: '73',
    manager_id: '4919',
    name: 'Backyard, Garage, Front of the house',
    order: '4',
  },
  { id: '74', manager_id: '4919', name: 'Before Leaving', order: '5' },
  { id: '75', manager_id: '4919', name: 'Leaving', order: '6' },
  { id: '76', manager_id: '4919', name: 'After leaving', order: '7' },
];

export const dummyJob: Partial<HKExtendedJob> = {
  jobSections: [],
  booking: {
    arrival: '2025/01/29',
    departure: '2025/01/30',
    property_uid: '6826',
  },
  property: {
    property_name: 'x LV-SL 7x3 Villa',
    owner_uid: '4919',
    propertyAddress: '3530 Sandcliff Lane, Las Vegas, Nevada, US',
    vitals: {
      access_garage: '',
      access_elevator: '',
      access_gate: 'Security door and side gates: c2019z',
      access_notes: '',
      access_parking_spots: '',
      access_wifi_name: 'WelcomeHome5',
      access_wifi_password: 'lasvegas777',
      access_howto:
        'Please park inside the garage immediately upon arrival and enter the house through the garage. If you’re not driving, enter through the front door.',
      access_parking_location: '',
      access_lockbox_location:
        'Garage door: 9476-enter\r\nkeypad code for sliding gate: 22-(right arrow)\r\ngarage-to-house door: 202255',
      access_lockbox_code:
        'Front Door: double deadbolt, locked from guest use.',
      access_intercom: '',
      access_checkin_information: '',
      access_checkout_information: '',
    },
  },
  accessTokens: [],
};

export const checklistOptions: HKChecklistOption[] = [
  {
    value: HKPerformType.Personally,
    label: 'Yes',
    description: 'I am going to perform the checklist fully or partially.',
    icon: <PersonIcon fill="black" width={24} />,
  },
  {
    value: HKPerformType.Assigned,
    label: 'No',
    description: 'I am going to assign the whole checklist to other people.',
    icon: <PeopleOutlineIcon fill="black" width={24} />,
  },
];
