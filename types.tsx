/* eslint-disable camelcase */
import { ReactNode } from 'react';
import { GeocodingFeature } from '@mapbox/search-js-core';
import { TaskPriorities, TaskRecurringIntervals } from '~dummyData';

export type RootStackParamList = {
  Root: undefined;
  NotFound: undefined;
  SkillsAfterRegistrationScreen: undefined;
  ActivateAccountScreen: undefined;
  SignIn: undefined;
  SignUp: undefined;
  Support: undefined;
  ForgotPassword: undefined;
  ResetPassword: undefined;
};

export type BottomTabParamList = {
  'My Jobs': undefined;
  'Available Jobs': undefined;
  'My Invoices': undefined;
  Profile: undefined;
  'House Keeping': undefined;
  More: undefined;
};

export type MyTasksParamList = {
  MyTasksScreen: undefined;
  TaskScreen: undefined;
  FinishTaskScreen: undefined;
  TaskCompletionScreen: undefined;
  TaskAssignmentScreen: undefined;
  TaskHistoryScreen: undefined;
};

export type AvailableTasksParamList = {
  AvailableTasksScreen: undefined;
  TaskScreen: { id: number; copiedForToday: boolean };
  AddTaskScreen: { taskId: number };
  TaskSettingsScreen: undefined;
  RecommendedTaskListingScreen: {
    headerTitle: string;
    compactModeEnabled: boolean;
    propertyId?: number;
    date?: string;
    urgent?: boolean;
    overdue?: boolean;
    textSearch?: string;
  };
  TaskHistoryScreen: { taskId: number; description: string };
  UnblockTaskScreen: { id: number };
};

export type HouseKeepingParamList = {
  HouseKeepingScreen: {
    jobId?: number;
    propertyId?: number;
    performer?: 'helper' | 'leader';
    checklistToken?: string;
  };
};

export type MyInvoicesParamList = {
  MyInvoicesScreen: undefined;
  FinishTaskScreen: undefined;
  TaskCompletionScreen: undefined;
  TaskAssignmentScreen: undefined;
  PaymentDetailsScreen: undefined;
  LogPaymentScreen: undefined;
};

export type PropertiesParamList = {
  PropertiesScreen: undefined;
  PropertyScreen: undefined;
  PropertyFormScreen: undefined;
};

export type ProfileParamList = {
  ProfileScreen: undefined;
  PersonalInformationScreen: undefined;
  PasswordScreen: undefined;
  TeamScreen: undefined;
  SkillsAfterRegistrationScreen: undefined;
  SkillsScreen: undefined;
  MemberScreen: undefined;
  AccountEditScreen: undefined;
};

export type MoreParamList = {
  AdminScreen: undefined;
  TeamScreen: undefined;
  MemberScreen: {
    accountId?: number;
    memberId?: number;
    userId?: number;
    invitationId?: number;
  };
  AccountEditScreen: undefined;
  AllTasksScreen: undefined;
  TaskScreen: {
    id: number;
    copiedForToday?: boolean;
    backnavScreenName?: string;
  };
  AddTaskScreen: undefined;
  TaskSettingsScreen: undefined;
  FinishTaskScreen: undefined;
  PropertiesScreen: undefined;
  PropertyScreen: { id: number; backnavTo?: { stack: string; screen: string } };
  PropertyFormScreen: { propertyId: number };
  TaskCompletionScreen: undefined;
  TaskAssignmentScreen: { id: number; showMinimal?: boolean };
  InvoicesMenuScreen: undefined;
  InvoicesScreen: {
    headerTitle: string;
    userId?: number;
    subjectMonth?: string;
  };
  LogPaymentScreen: {
    paymentId: number;
    selectedMonth?: string;
    userId?: number | null;
  };
  PaymentDetailsScreen: { paymentId: number };
  ProfitMarginScreen: undefined;
  UnpaidInvoicesScreen: undefined;
  PropertiesMenuScreen: undefined;
  CalendarScreen: undefined;
  TaskHistoryScreen: undefined;
  MoreScreen: undefined;
  ActivitiesScreen: undefined;
  InvoicePDFScreen: { downloadedPdfUri: string };
  AdminSkillsScreen: undefined;
  AddSkillScreen: undefined;
  UnblockTaskScreen: { id: number };
  SupportScreen: undefined;
};

export type Property = {
  calendarLink: string | null;
  id: number;
  name: string;
  address: string;
  accessInformation: string;
  notes: string;
  calendarDates: CalendarDate[];
  deletedAt: Date;
  isSuspended: boolean;
  internalNotes?: string;
  housekeepingEnabled?: boolean;
};

export type Priority = {
  id: number;
  name: string;
  info: string;
};

export type RecurringInterval = {
  id: number;
  name: string;
};

export type Skill = {
  id: number;
  name: string;
  accountId?: number;
  slug?: string | null;
};

export type Task = {
  accountId: number;
  createdAt: Date;
  creatorUserId: number;
  description: string;
  hasFinishedAssignment: boolean;
  id: number;
  media: string;
  nextRepetitionNumber?: number;
  preferredDate: string;
  preferredEndTime: string;
  preferredStartTime: string;
  priority: TaskPriorities;
  property: Property;
  recurring: TaskRecurringIntervals;
  requiredSkillId: number;
  requiredSkills: Skill[];
  taskAssignment: TaskAssignment;
  tasksDeletedAt: Date;
  thumbnail: string;
  updatedAt: Date;
  completions?: TaskAssignment[];
  requiredSkillIds: number[];
  comments: Comment[];
  blockedUsers?: User[];
  propertyAvailability: string;
  deletedAt: string;
};

export type TaskLog = {
  id: number;
  user: {
    id: number;
    name: string;
  };
  task: {
    id: number;
    description: string;
  };
  taskAssignmentId: number | null;
  createdAt: Date;
  actionType: number;
  additionalInfo: string;
};

export type GroupedTask = {
  name: string;
  sum: number;
  tasks: Task[];
};

export type GroupedTaskAssignment = {
  name: string;
  sum: number;
  tasks: TaskAssignment[];
};

type Media = {
  createdAt: string;
  id: number;
  mediaUrl: string;
  taskUserId: number;
  type: string;
  updatedAt: string;
};

export type TaskAssignment = {
  preferredDate: string;
  id: number;
  thumbnail: string;
  video: string;
  comments: string;
  costOfLabor: number;
  costOfMaterials: number;
  finishedAt?: Date;
  receipts: Media[];
  task: Task;
  user: User;
  timeSpent: string;
  hourlyRate: string;
  userId: number;
  repetitionNumber: number;
  createdAt: Date;
  expiredAt: Date;
};

export type Role = {
  id: number;
  name: string;
};

export type Payment = {
  id: number;
  subjectMonth: Date;
  amount: string;
  date: Date;
  description: string;
  media: string;
  userId: number;
};

export type SignUp = {
  name: string;
  email: string;
  password: string;
  invitationCode: string;
  accountName: string;
};

export type Timezone = {
  value: string;
  label: string;
  offset: number;
  abbrev: string;
  altName: string;
};

export type UserAccount = {
  userId: number;
  accountId: number;
  role: number;
  isSkillsetLocked: boolean;
  hourlyRate: number | null;
  deletedAt: Date | null;
};

export type Account = {
  id: number;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  timezone: Timezone;
  profitMargin: number;
  isProfitMarginEnabled: boolean;
  pivot: UserAccount;
  isActive: boolean;
};

export type User = {
  id: number;
  name: string;
  email: string;
  password: string;
  phone: string;
  address: string;
  taxId: string;
  skillsetIds: number[];
  isDeleted?: boolean;
  accounts: Account[];
  balance?: number;
};

export type CalendarDate = {
  id: number;
  date: string;
  checkInDate: string;
  propertyId: number;
  createdAt: string;
  updatedAt: string;
};

export type MediaFile = {
  id: string;
  type: string | null;
  thumbnailUri: string;
  uri: string;
};

export type TaskLogActionType = {
  id: number;
  name: string;
  icon: string;
};

export type BackgroundUpload = {
  uploadUrl: string;
  localUrl: string;
  type: BackgroundUploadFileType;
  taskId?: string;
};

export type BackgroundUploadFileType =
  | 'initial-media'
  | 'initial-media-thumbnail'
  | 'completion-media'
  | 'completion-media-thumbnail'
  | 'receipt'
  | 'media';

export type Comment = {
  id: number;
  taskId: number;
  user: User;
  createdAt: string;
  text: string;
};

export type PaginatedResult<T> = {
  currentPage: number;
  data: T[];
  firstPageUrl: string;
  from: number;
  lastPage: number;
  lastPageUrl: string;
  links: { url: boolean; label: string; active: boolean }[];
  nextPageUrl: string | null;
  path: string;
  perPage: number;
  prevPageUrl: string | null;
  to: number;
  total: number;
};

export type DayBlockItem = { date: Date; properties: Property[] };

export type TaskGroup = {
  date: string | number;
  properties?: PropertyTask[];
  tasks?: Task[];
};

export type PropertyTask = {
  propertyName: string;
  tasks: Task[];
};

export type HKSection = {
  id: number;
  name: string;
  order: number;
  translations: Record<string, string>;
  createdAt: string;
  updatedAt: string;
  accountId: number;
};

export type HKTask = {
  id: number;
  name: string;
  sectionId: number;
  order?: number;
  person1: boolean;
  person2: boolean;
  translations: Record<string, string>;
  createdAt: string;
  updatedAt: string;
  accountId: number;
  sectionName: string;
  all: boolean;
};

export type HKChecklist = 'person1' | 'person2' | 'all';

export type HKPerformOption = {
  value: HKChecklist;
  label: string;
  description: string;
  icon: ReactNode;
};

export enum HKPerformType {
  Personally = 'personally',
  Assigned = 'assigned',
}

export type HKChecklistOption = {
  value: HKPerformType;
  label: string;
  description: string;
  icon: ReactNode;
};

export type HKJob = {
  id: number | null;
  performType: HKPerformType;
  leaderPerforms: HKChecklist | null;
  leaderName: string;
  helperName: string;
};

export type HKAddJobResponse = Omit<HKJob, 'id'> & {
  id: number;
  propertyId: number;
  createdAt: string;
  updatedAt: string;
};

export type HKProperty = {
  id: number;
  name: string;
  address: string;
  accessInformation: string;
  notes: string;
  calendarLink: string;
  accountId: number;
  createdAt: string;
  updatedAt: string;
  calendarSyncedAt: string;
  deletedAt: string | null;
  isSuspended: boolean;
  housekeepingEnabled: boolean;
};

export type HKVitals = Record<string, string>;

export type HKChecklistToken = {
  id: number;
  token: string;
  checklist: HKChecklist;
  createdAt: string;
  updatedAt: string;
  jobId: number;
};

export type HKExtendedJob = HKJob & {
  createdAt: string;
  updatedAt: string;
  jobSections: HKJobSection[];
  propertyId: number;
  property: HKProperty;
  checklistTokens: HKChecklistToken[];
};

export type HKJobSection = {
  id: number;
  checklist: HKChecklist;
  items: string[];
  status: 'started' | 'completed';
  createdAt: string;
  startedAt: string;
  updatedAt: string;
  completedAt: string;
  location: {
    coordinates: { lat: number; lng: number };
    address: GeocodingFeature;
    timezone: string;
  } | null;
  jobId: number;
  sectionId: number;
};

export type HKUser = {
  name: string;
  role: 'leader' | 'helper';
  performing: HKChecklist;
};
