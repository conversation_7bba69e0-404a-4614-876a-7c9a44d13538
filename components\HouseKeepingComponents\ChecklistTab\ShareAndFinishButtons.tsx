import React, { ComponentProps, useState } from 'react';
import { StyleService, useStyleSheet } from '@ui-kitten/components';
import Stack from '~components/HouseKeepingComponents/UI/Stack';
import SharePDFButton from '~components/HouseKeepingComponents/ChecklistTab/SharePDFButton';
import FinishButton from '~components/HouseKeepingComponents/ChecklistTab/FinishButton';

type Props = ComponentProps<typeof Stack> & {
  onClickFinish: () => void;
  disabled: boolean;
  pdfReportPath: string;
};

const ShareAndFinishButtons = ({
  onClickFinish,
  disabled,
  pdfReportPath,
  style,
  ...props
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const [pdfReportShared, setPdfReportShared] = useState(false);

  const handleClickSharePDFReport = () => setPdfReportShared(true);

  return (
    <Stack style={[styles.container, style]} {...props}>
      <SharePDFButton
        url={pdfReportPath}
        onClickShare={handleClickSharePDFReport}
        disabled={disabled}
        shared={pdfReportShared}
      />
      <FinishButton
        onClick={onClickFinish}
        disabled={!pdfReportShared || disabled}
      />
    </Stack>
  );
};

export default ShareAndFinishButtons;

const themedStyles = StyleService.create({
  container: {
    gap: 12,
    paddingBottom: 12,
    backgroundColor: 'white',
  },
});
