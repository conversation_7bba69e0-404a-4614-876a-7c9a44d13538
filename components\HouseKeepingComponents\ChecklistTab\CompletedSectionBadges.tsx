import React from 'react';
import { Icon, StyleService, useStyleSheet } from '@ui-kitten/components';
import theme from '~/theme.json';
import { ClockOutlineIcon, PinIcon } from '~/components/Icon';
import Badge from '~components/HouseKeepingComponents/UI/Badge';
import Stack from '~components/HouseKeepingComponents/UI/Stack';
import Flex from '~components/HouseKeepingComponents/UI/Flex';
import { AlarmIcon } from '~components/HouseKeepingComponents/Shared/MaterialIcons';

type Props = {
  completedAtWithTimezone: string | null;
  completedIn: string;
  totalTimeElapsed: string;
  showTotal: boolean;
  namePreferred: string | undefined;
};

const CompletedSectionBadges = ({
  completedAtWithTimezone,
  completedIn,
  totalTimeElapsed,
  showTotal,
  namePreferred,
}: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <Flex style={styles.completedBadges}>
      <Icon
        name="checkmark-circle-2"
        fill={theme['color-success-600']}
        width={70}
        style={styles.icon}
      />
      <Stack style={styles.badgeGroup}>
        {completedAtWithTimezone && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={ClockOutlineIcon}
          >
            {completedAtWithTimezone}
          </Badge>
        )}
        {completedIn && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={AlarmIcon}
          >
            Section: {completedIn}
          </Badge>
        )}
        {showTotal && (
          <Badge
            status="success"
            size="large"
            style={styles.badge}
            leftSection={AlarmIcon}
          >
            Total: {totalTimeElapsed}
          </Badge>
        )}
        {namePreferred && (
          <Badge size="large" style={styles.badge} leftSection={PinIcon}>
            {namePreferred}
          </Badge>
        )}
      </Stack>
    </Flex>
  );
};

export default CompletedSectionBadges;

const themedStyles = StyleService.create({
  completedBadges: {
    gap: 12,
    padding: 5,
    alignItems: 'center',
    flexWrap: 'nowrap',
  },
  badgeGroup: {
    flex: 1,
  },
  icon: {
    flexShrink: 0,
    height: '100%',
    aspectRatio: 1,
  },
  badge: {
    paddingLeft: 5,
  },
});
