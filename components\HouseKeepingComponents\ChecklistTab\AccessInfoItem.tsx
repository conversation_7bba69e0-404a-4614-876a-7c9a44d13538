import React from 'react';
import { View, FlatList } from 'react-native';
import {
  StyleService,
  Text,
  Divider,
  useStyleSheet,
} from '@ui-kitten/components';

import Flex from '~components/HouseKeepingComponents/UI/Flex';

type Props = {
  icon: React.JSX.Element;
  title: string;
  items: {
    title: string;
    content: string;
  }[];
};

const AccessInfoItem = ({ icon, title, items }: Props) => {
  const styles = useStyleSheet(themedStyles);

  return (
    <FlatList
      data={items}
      keyExtractor={item => item.title}
      ListHeaderComponent={
        <>
          <View style={styles.sectionHeader}>
            {icon}
            <Text style={styles.sectionTitle}>{title}</Text>
          </View>
          <Divider />
        </>
      }
      renderItem={({ item: { title: itemTitle, content } }) => (
        <>
          <Flex style={styles.itemContainer}>
            <View style={styles.itemLabelContainer}>
              <Text style={styles.itemLabel}>{itemTitle}</Text>
            </View>
            <View style={styles.itemValueContainer}>
              <Text style={styles.itemValue}>{content}</Text>
            </View>
          </Flex>
          <Divider />
        </>
      )}
    />
  );
};

export default AccessInfoItem;

const themedStyles = StyleService.create({
  sectionHeader: {
    gap: 10,
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: 12,
  },
  itemContainer: {
    paddingVertical: 12,
  },
  itemLabel: {
    fontSize: 14,
    fontWeight: 700,
  },
  itemValue: {
    fontSize: 14,
  },
  sectionTitle: {
    fontSize: 18,
  },
  itemLabelContainer: {
    flex: 2,
  },
  itemValueContainer: {
    flex: 3,
  },
});
