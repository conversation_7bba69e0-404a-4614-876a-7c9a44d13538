import useClient from './useClient';
import {
  HKAddJobResponse,
  HKChecklist,
  HKExtendedJob,
  HKJob,
  HKJobSection,
  HKProperty,
  HKSection,
  HKTask,
} from '~/types';

const useHouseKeepingApi = () => {
  const client = useClient();

  const addJob = async ({
    job,
    propertyId,
  }: {
    job: HKJob;
    propertyId: HKProperty['id'];
  }) => {
    const apiJob = {
      job_id: job.id,
      leader_performs: job.leaderPerforms,
      leader_name: job.leaderName,
      helper_name: job.helperName,
      perform_type: job.performType,
      property_id: propertyId,
    };
    const newJob = await client
      .post('/checklist/jobs', apiJob)
      .then(res => res.data as HKAddJobResponse);
    return newJob;
  };

  const startChecklist = async ({
    jobId,
    checklist,
    checklistToken = '',
  }: {
    jobId: <PERSON>Job['id'];
    checklist: HKChecklist;
    checklistToken?: string;
  }) => {
    const startedJob = await client
      .post(`/checklist/jobs/${jobId}/start`, {
        checklist,
        checklist_token: checklistToken,
      })
      .then(res => res.data as HKExtendedJob);
    return startedJob;
  };

  const completeJobSection = async ({
    jobSection,
    checklistToken = '',
  }: {
    jobSection: HKJobSection;
    checklistToken?: string;
  }) => {
    const apiJobSection = {
      id: jobSection.id,
      items: jobSection.items,
      location: {
        coordinates: jobSection.location?.coordinates,
        address: jobSection.location?.address,
        timezone: jobSection.location?.timezone,
      },
      checklist_token: checklistToken,
    };

    const result = await client
      .post('/checklist/complete-section', apiJobSection)
      .then(res => res.data as HKExtendedJob);

    return result;
  };

  const fetchJob = async (jobId: HKJob['id'], checklistToken = '') => {
    const job = await client
      .get(`/checklist/jobs/${jobId}`, {
        params: {
          checklist_token: checklistToken,
        },
      })
      .then(res => res.data as HKExtendedJob);
    return job;
  };

  const fetchItems = async (checklistToken = '') => {
    const items = await client
      .get('/checklist/items', {
        params: {
          checklist_token: checklistToken,
        },
      })
      .then(res => res.data as HKTask[]);
    return items;
  };

  const fetchSections = async (checklistToken = '') => {
    const sections = await client
      .get('/checklist/sections', {
        params: {
          checklist_token: checklistToken,
        },
      })
      .then(res => res.data as HKSection[]);
    return sections;
  };

  const finishJob = async ({
    jobId,
    checklistToken = '',
  }: {
    jobId: HKJob['id'];
    checklistToken?: string;
  }) => {
    const result = await client
      .post(`/checklist/jobs/${jobId}/finish`, {
        checklist_token: checklistToken,
      })
      .then(res => res.data as HKExtendedJob);
    return result;
  };

  return {
    addJob,
    startChecklist,
    completeJobSection,
    fetchJob,
    fetchSections,
    fetchItems,
    finishJob,
  };
};

export default useHouseKeepingApi;
