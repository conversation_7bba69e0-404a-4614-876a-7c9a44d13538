import React from 'react';
import { FlatList } from 'react-native';
import { HKExtendedJob } from '~/types';
import { UnlockIcon, FileTextIcon } from '~/components/Icon';
import { ParkingIcon } from '~components/HouseKeepingComponents/Shared/MaterialIcons';
import AccessInfoItem from '~components/HouseKeepingComponents/ChecklistTab/AccessInfoItem';

type Props = {
  job: HKExtendedJob;
};

const AccessInfoList = ({ job }: Props) => {
  const {
    property: {
      propertyAddress,
      vitals: {
        access_elevator: accessElevator,
        access_garage: accessGarage,
        access_gate: accessGate,
        access_parking_location: accessParkingLocation,
        access_notes: accessNotes,
        access_parking_spots: accessParkingSpots,
        access_howto: accessHowto,
        access_wifi_password: accessWifiPassword,
        access_wifi_name: accessWifiName,
        access_lockbox_location: accessLockboxLocation,
        access_lockbox_code: accessLockboxCode,
        access_intercom: accessIntercom,
      },
    },
  } = job;

  const accessInfos = [
    {
      icon: <UnlockIcon fill="black" width={24} />,
      title: 'How to get into the Property',
      items: [
        {
          title: 'Lockbox or Smartlock Location',
          content: accessLockboxLocation,
        },
        {
          title: 'Lockbox or Smartlock Code',
          content: accessLockboxCode,
        },
        {
          title: 'Property Address',
          content: propertyAddress,
        },
        {
          title: 'Intercom Number',
          content: accessIntercom,
        },
        {
          title: 'Gate Access Code',
          content: accessGate,
        },
        {
          title: 'Elevator Access Code',
          content: accessElevator,
        },
        {
          title: 'Garage Access Code',
          content: accessGarage,
        },
        {
          title: 'How to get in',
          content: accessHowto,
        },
      ],
    },
    {
      icon: <ParkingIcon fill="black" width={24} />,
      title: 'Where to park your car',
      items: [
        {
          title: 'Parking Location',
          content: accessParkingLocation,
        },
        {
          title: 'Parking Spots',
          content: accessParkingSpots,
        },
      ],
    },
    {
      icon: <FileTextIcon fill="black" width={24} />,
      title: 'Other things to note',
      items: [
        {
          title: 'Notes',
          content: accessNotes,
        },
        {
          title: 'WiFi Name',
          content: accessWifiName,
        },
        {
          title: 'WiFi Password',
          content: accessWifiPassword,
        },
      ],
    },
  ];

  return (
    <FlatList
      data={accessInfos}
      keyExtractor={item => item.title}
      contentContainerStyle={{ gap: 16 }}
      renderItem={({ item: { icon, title, items } }) => {
        const filteredItems = items.filter(item => item.content);
        return (
          <AccessInfoItem icon={icon} title={title} items={filteredItems} />
        );
      }}
    />
  );
};

export default AccessInfoList;
