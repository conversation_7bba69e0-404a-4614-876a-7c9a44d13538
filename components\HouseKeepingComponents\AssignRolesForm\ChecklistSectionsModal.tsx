import React from 'react';
import { HKChecklist, HKSection, HKTask } from '~/types';
import ChecklistSections from '~components/HouseKeepingComponents/ChecklistTab/ChecklistSections';
import Modal from '~components/HouseKeepingComponents/Shared/Modal';
import { formatPreviewName } from '~/hk-helpers';

type Props = {
  sections: HKSection[];
  tasks: HKTask[];
  checklist: HKChecklist;
  onClose: () => void;
};

const ChecklistSectionsModal = ({
  sections,
  tasks,
  checklist,
  onClose,
}: Props) => (
  <Modal
    visible={!!checklist}
    onClose={onClose}
    title={`Preview ${formatPreviewName(checklist)}`}
  >
    <ChecklistSections
      sections={sections}
      checklist={checklist}
      tasks={tasks.filter(task => task[checklist])}
    />
  </Modal>
);

export default ChecklistSectionsModal;
