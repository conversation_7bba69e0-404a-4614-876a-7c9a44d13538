import React, { ReactNode, useMemo } from 'react';
import {
  CheckBox,
  StyleService,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import * as Yup from 'yup';
import { useToast } from 'react-native-toast-notifications';
import { Formik } from 'formik';
import { FlatList, Platform } from 'react-native';
import { useGeocodingCore } from '@mapbox/search-js-react';
import { VisibilitySensor } from '@futurejj/react-native-visibility-sensor';
import useUser from '~api/useUser';
import {
  HKSection,
  HKChecklist,
  HKTask,
  HKExtendedJob,
  HKUser,
  HKJobSection,
} from '~types';
import ChecklistSectionHeader from '~components/HouseKeepingComponents/ChecklistTab/ChecklistSectionHeader';
import CompletedSectionBadges from '~components/HouseKeepingComponents/ChecklistTab/CompletedSectionBadges';
import { getLocation, intervalToHuman } from '~hk-helpers';
import useTimezone from '~hooks/useTimezone';
import SpinnerButton from '~components/SpinnerButton';
import theme from '~/theme.json';

type Props = {
  section: HKSection;
  checklist: HKChecklist;
  tasks: HKTask[];
  job?: HKExtendedJob | null;
  user?: HKUser | null;
  onComplete?: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  disabled?: boolean;
  setStickyHeader?: (header: ReactNode | null) => void;
};

type FormValues = {
  taskIds: string[];
};

const ChecklistSection = ({
  job,
  user,
  section,
  checklist,
  tasks,
  onComplete,
  disabled,
  setStickyHeader,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const toast = useToast();
  const { formatInTimeZone } = useTimezone();
  const { getUser } = useUser();
  const jobSection =
    job?.jobSections.find(
      js => js.sectionId === section.id && js.checklist === checklist,
    ) || null;

  const geocodingCore = useGeocodingCore({
    accessToken: process.env.MAPBOX_ACCESS_TOKEN,
  });
  const { id: sectionId, name: sectionName } = section;
  const jobSectionId = jobSection?.sectionId;
  const completed = jobSection?.status === 'completed';
  const address = jobSection?.location?.address;
  const namePreferred = address?.properties?.name_preferred;
  const completedAt = jobSection?.completedAt;
  const completedAtWithTimezone = formatInTimeZone(completedAt, 'PP p');
  const firstSectionStartedAt = job?.jobSections[0]?.startedAt;
  const startedAt = jobSection?.startedAt;
  const completedIn = intervalToHuman(startedAt, completedAt);
  const totalTimeElapsed = intervalToHuman(firstSectionStartedAt, completedAt);
  const showTotal =
    firstSectionStartedAt && firstSectionStartedAt !== startedAt;

  const schema = useMemo(() => {
    const allTaskIds = tasks.map(task => task.id.toString());

    return Yup.object().shape({
      taskIds: Yup.array()
        .of(Yup.string())
        .required('Please complete all tasks.')
        .test('all-tasks-selected', 'Please complete all tasks.', value => {
          if (!value || !Array.isArray(value)) {
            return false;
          }
          return allTaskIds.every(taskId => value.includes(taskId.toString()));
        }),
    });
  }, [tasks]);

  const onSubmit = async ({ taskIds }: FormValues) => {
    if (!job || !user) {
      return;
    }

    const { id: jobId } = job;
    const { performing } = user;

    if (!jobId || !jobSectionId || !startedAt) {
      return;
    }

    let location = null;
    try {
      location = await getLocation(geocodingCore);
    } catch {
      toast.show(
        'Could not determine location, please check your browser settings.',
        {
          type: 'warning',
          dangerColor: theme['color-danger-600'],
          placement: 'top',
          style: { marginTop: Platform.OS === 'web' ? 56 : 0 },
        },
      );
      return;
    }

    const {
      accounts: {
        0: { timezone: { value: timezone = 'America/Los_Angeles' } = {} },
      },
    } = await getUser();

    onComplete?.({
      ...jobSection,
      jobId,
      sectionId,
      checklist: performing,
      items: taskIds as string[],
      status: 'completed',
      startedAt,
      location: {
        ...location,
        timezone,
      },
      completedAt: new Date().toISOString(),
    })
      .then(() => {
        toast.show('Section completed.', {
          type: 'success',
          successColor: theme['color-success-600'],
          placement: 'top',
          style: { marginTop: Platform.OS === 'web' ? 56 : 0 },
        });
      })
      .catch(() => {
        toast.show('Could not save section. Please try again later.', {
          type: 'warning',
          dangerColor: theme['color-danger-600'],
          placement: 'top',
          style: { marginTop: Platform.OS === 'web' ? 56 : 0 },
        });
      });
  };

  const checklistSectionHeader = useMemo(
    () => (
      <ChecklistSectionHeader
        title={sectionName}
        description="Please complete all tasks before completing the section."
        firstSectionStartedAt={firstSectionStartedAt}
        startedAt={startedAt}
        completedAt={completedAt}
      />
    ),
    [sectionName, firstSectionStartedAt, startedAt, completedAt],
  );

  const initValues = {
    taskIds: jobSection?.items || [],
  };

  return (
    <Formik
      initialValues={initValues}
      onSubmit={onSubmit}
      enableReinitialize
      validationSchema={schema}
      validateOnMount
    >
      {({
        values: { taskIds },
        handleSubmit,
        setFieldValue,
        isValid,
        errors,
        touched,
        isSubmitting,
      }) => (
        <FlatList
          data={tasks}
          keyExtractor={({ id }) => id.toString()}
          style={[styles.checklistContainer, completed && styles.completed]}
          ListHeaderComponent={
            <VisibilitySensor
              onChange={(isVisible: boolean) =>
                setStickyHeader?.(isVisible ? null : checklistSectionHeader)
              }
              disabled={disabled}
              threshold={{
                top: 250,
                bottom: 250,
              }}
            >
              {checklistSectionHeader}
            </VisibilitySensor>
          }
          renderItem={({ item: { id, name } }) => {
            const checked = taskIds.includes(id.toString());
            return (
              <CheckBox
                key={id.toString()}
                checked={checked}
                status="info"
                style={[
                  styles.checkbox,
                  !completed && checked && styles.checked,
                ]}
                onChange={() => {
                  if (!checked) {
                    setFieldValue('taskIds', [...taskIds, id.toString()]);
                  } else {
                    setFieldValue(
                      'taskIds',
                      taskIds.filter(taskId => taskId !== id.toString()),
                    );
                  }
                }}
                disabled={disabled}
              >
                {name}
              </CheckBox>
            );
          }}
          ListFooterComponentStyle={{ gap: 16 }}
          ListFooterComponent={
            <>
              {!disabled && (touched.taskIds || errors.taskIds) ? (
                <Text style={styles.error}>{errors.taskIds}</Text>
              ) : null}
              {completed ? (
                <CompletedSectionBadges
                  completedAtWithTimezone={completedAtWithTimezone}
                  completedIn={completedIn}
                  totalTimeElapsed={totalTimeElapsed}
                  showTotal={!!showTotal}
                  namePreferred={namePreferred}
                />
              ) : (
                <SpinnerButton
                  text="Complete Section"
                  size="large"
                  status="info"
                  onPress={() => handleSubmit()}
                  isLoading={isSubmitting}
                  disabled={!isValid || disabled}
                />
              )}
            </>
          }
        />
      )}
    </Formik>
  );
};

export default ChecklistSection;

const themedStyles = StyleService.create({
  checklistContainer: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
    padding: 12,
    gap: 16,
    borderRadius: 4,
  },
  checkbox: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#dee2e6',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  error: {
    color: 'red',
    marginTop: 8,
  },
  completed: {
    backgroundColor: 'color-success-transparent-100',
  },
  checked: {
    borderColor: 'color-info-500',
  },
});
