import React from 'react';
import { Text } from '@ui-kitten/components';
import { Platform } from 'react-native';
import { openURL } from 'expo-linking';
import { useMutation, useQueryClient } from 'react-query';
import { StackScreenProps } from '~node_modules/@react-navigation/stack/lib/typescript/src';
import HouseKeepingPage from '~components/HouseKeepingComponents/HouseKeepingPage';
import {
  HouseKeepingParamList,
  HKJob,
  HKJobSection,
  HKPerformType,
} from '~types';

import { whoAmI, pdfReportPath } from '~hk-helpers';
import useHouseKeepingApi from '~api/useHouseKeepingApi';
import useHKItems from '~queries/useHKItems';
import useHKSections from '~queries/useHKSections';
import useHKJob from '~queries/useHKJob';

type Props = StackScreenProps<HouseKeepingParamList, 'HouseKeepingScreen'>;

const keeperId = '77';

const HouseKeepingScreen = ({ route, navigation }: Props) => {
  const {
    jobId = 0,
    performer,
    propertyId = 0,
    checklistToken,
  } = route.params || {};

  const queryClient = useQueryClient();
  const { addJob, completeJobSection, startChecklist, finishJob } =
    useHouseKeepingApi();
  const {
    job,
    isLoading: jobIsLoading,
    refetch: refetchHKJob,
  } = useHKJob({
    jobId,
    checklistToken,
  });
  const { items: tasks, isLoading: itemsIsLoading } = useHKItems(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );
  const { sections, isLoading: sectionsIsLoading } = useHKSections(
    !!propertyId || !!jobId || !!checklistToken,
    checklistToken,
  );

  const { mutateAsync: startChecklistMutate } = useMutation(startChecklist, {
    onSuccess: () => {
      queryClient.invalidateQueries(['job', jobId, checklistToken]);
    },
  });

  const { mutateAsync: updateJobSectionMutate } = useMutation(
    completeJobSection,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['job', jobId, checklistToken]);
      },
    },
  );

  const { mutateAsync: addJobMutate } = useMutation(addJob, {
    onSuccess: () => {
      queryClient.invalidateQueries(['job', jobId, checklistToken]);
    },
  });

  const { mutateAsync: finishJobMutate } = useMutation(finishJob, {
    onSuccess: () => {
      queryClient.invalidateQueries(['job', jobId, checklistToken]);
    },
  });

  const isLoading = jobIsLoading || itemsIsLoading || sectionsIsLoading;

  if (
    (!propertyId || !jobId || !checklistToken) &&
    !isLoading &&
    (!job || !tasks || !sections)
  ) {
    return <Text style={{ color: 'red', padding: 30 }}>Access Denied.</Text>;
  }

  if ((!!propertyId || !!jobId || !!checklistToken) && !job && !isLoading) {
    return (
      <Text style={{ color: 'red', padding: 30 }}>
        This housekeeping job has not been created for this property.
      </Text>
    );
  }

  let helperView = true;
  if (checklistToken) {
    if (
      (job?.leaderPerforms === 'person1' &&
        job.checklistTokens[0].token === checklistToken) ||
      (job?.leaderPerforms === 'person2' &&
        job.checklistTokens[1].token === checklistToken)
    ) {
      helperView = false;
    }
  }

  const user = whoAmI(
    job || null,
    checklistToken ? helperView : performer === 'helper',
  );
  const { performType } = job || {};

  const handleSaveAssignRolesForm = async (values: HKJob) => {
    if (!propertyId && !values.id) return;
    const updatedJob = await addJobMutate({
      job: values,
      propertyId,
    });

    if (!values.id) navigation.setParams({ jobId: updatedJob.id });
  };

  const handleClickPDFReport = () => {
    if (!job) return;
    const url = pdfReportPath(
      'en',
      jobId || job!.id,
      keeperId,
      checklistToken || job?.checklistTokens[0].token,
    );
    if (Platform.OS === 'web') {
      window.open(url, '_blank');
    } else {
      openURL(url);
    }
  };

  const handleStartChecklist = async () => {
    if (!job || !user?.performing) return;
    await startChecklistMutate({
      jobId,
      checklist: user.performing,
    });
  };

  const handleCompleteSection = async (jobSection: HKJobSection) => {
    if (!jobId || !jobSection) return;
    await updateJobSectionMutate({ jobSection, checklistToken });
  };

  const handleClickFinish = async () => {
    if (!jobId && !checklistToken) return;
    await finishJobMutate({ jobId, checklistToken });
  };

  return (
    <HouseKeepingPage
      job={job || null}
      user={user}
      isLoading={isLoading}
      sections={sections || []}
      onStartChecklist={handleStartChecklist}
      onCompleteSection={handleCompleteSection}
      tasks={tasks || []}
      onClickFinish={handleClickFinish}
      onClickPDFReport={handleClickPDFReport}
      pdfReportPath={pdfReportPath(
        'en',
        jobId || job!.id,
        keeperId,
        checklistToken,
      )}
      onSaveAssignRolesForm={handleSaveAssignRolesForm}
      updateJob={refetchHKJob}
      canEditRoles
      onlyAssigning={performType === HKPerformType.Assigned}
    />
  );
};

export default HouseKeepingScreen;
