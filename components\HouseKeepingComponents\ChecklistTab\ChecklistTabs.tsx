import React, { Dispatch, ReactNode, SetStateAction, useEffect } from 'react';
import {
  StyleService,
  Tab,
  TabBar,
  Text,
  useStyleSheet,
} from '@ui-kitten/components';
import { View } from 'react-native';
import { checklistLabel, enabledTabs } from '~/hk-helpers';
import {
  HKChecklist,
  HKExtendedJob,
  HKJobSection,
  HKSection,
  HKTask,
  HKUser,
} from '~/types';
import usePerformOptions from '~/hooks/usePerformOptions';
import ChecklistTab from '~components/HouseKeepingComponents/ChecklistTab/ChecklistTab';

type Props = {
  sections: HKSection[];
  job: HKExtendedJob;
  user: HKUser | null;
  tasks: HKTask[];
  performing: HKChecklist | null;
  updateJob: () => void;
  onStartChecklist: () => Promise<void>;
  onCompleteSection: (
    jobSection: Omit<HKJobSection, 'completed_at'>,
  ) => Promise<void>;
  isLeader: boolean;
  selectedTab: HKChecklist | null;
  setSelectedTab: Dispatch<SetStateAction<HKChecklist>>;
  activeSection: HKSection | null;
  onlyAssigning?: boolean;
  setStickyHeader?: (header: ReactNode | null) => void;
};

const ChecklistTabs = ({
  sections,
  job,
  user,
  tasks,
  performing,
  updateJob,
  onStartChecklist,
  onCompleteSection,
  isLeader,
  selectedTab,
  setSelectedTab,
  activeSection,
  onlyAssigning,
  setStickyHeader,
}: Props) => {
  const styles = useStyleSheet(themedStyles);
  const allPerformOptions = usePerformOptions({ performPersonally: true });

  const performOptions = allPerformOptions
    .filter(({ value }) => enabledTabs(performing).includes(value))
    .map(({ icon, value, description }) => ({
      icon,
      label: checklistLabel(job, value),
      value,
      description,
    }));

  useEffect(() => {
    if (!performing) {
      return;
    }

    setSelectedTab(performing);
  }, [performing, setSelectedTab]);

  const tabIndex = performOptions.findIndex(
    ({ value }) => value === selectedTab,
  );

  const handleTabSelect = (index: number) => {
    const selectedChecklist = performOptions[index]?.value || null;
    updateJob();
    setSelectedTab(selectedChecklist);
  };

  return (
    <View style={styles.container}>
      <TabBar
        selectedIndex={tabIndex}
        onSelect={handleTabSelect}
        style={styles.tabBar}
        indicatorStyle={styles.indicator}
      >
        {performOptions.map(({ icon, value }) => (
          <Tab
            key={value}
            title={
              <View>
                <Text category="s2" status="info" style={styles.tabText}>
                  {checklistLabel(job, value)}
                </Text>
              </View>
            }
            icon={<View>{icon}</View>}
            style={styles.tab}
          />
        ))}
      </TabBar>
      {selectedTab && (
        <ChecklistTab
          checklist={selectedTab}
          job={job}
          user={user}
          tasks={tasks}
          sections={sections}
          performing={performing}
          onStartChecklist={onStartChecklist}
          onCompleteSection={onCompleteSection}
          isLeader={isLeader}
          onlyAssigning={onlyAssigning}
          activeSection={activeSection}
          setStickyHeader={setStickyHeader}
        />
      )}
    </View>
  );
};

export default ChecklistTabs;

const themedStyles = StyleService.create({
  container: {
    flex: 1,
    gap: 16,
  },
  tabText: {
    fontSize: 14,
  },
  tabBar: {
    color: 'color-info-500',
  },
  tab: {
    flexDirection: 'row',
    gap: 32,
    color: 'color-info-500',
  },
  indicator: {
    backgroundColor: 'color-info-500',
    color: 'color-info-500',
  },
});
