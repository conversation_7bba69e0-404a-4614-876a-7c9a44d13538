import React from 'react';
import { StyleService, Button } from '@ui-kitten/components';
import { HKExtendedJob, Property } from '~/types';
import Modal from '~components/HouseKeepingComponents/Shared/Modal';
import PropertyCard from '~components/PropertyCard';

type Props = {
  job: HKExtendedJob | null;
  opened: boolean;
  onClose: () => void;
};

const AccessInfoListModal = ({ job, opened, onClose }: Props) => {
  if (!job) {
    return null;
  }

  const propertyInto: Property = {
    calendarLink: null,
    id: Number(job.id),
    name: job.property.name,
    address: job.property.address,
    accessInformation: job.property.accessInformation,
    notes: job.property.notes,
    calendarDates: [],
    deletedAt: new Date(job.property.deletedAt || ''),
    isSuspended: job.property.isSuspended,
  };

  return (
    <Modal visible={opened} style={styles.modal} contentStyle={styles.content}>
      <PropertyCard {...propertyInto} showDetails />
      <Button size="large" onPress={onClose} style={styles.button}>
        Close
      </Button>
    </Modal>
  );
};

export default AccessInfoListModal;

const styles = StyleService.create({
  modal: {
    flex: 1,
    margin: 0,
    width: '100%',
  },
  content: {
    flex: 1,
    paddingTop: 42,
    paddingBottom: 42,
    paddingHorizontal: 0,
  },
  button: {
    marginTop: 'auto',
  },
});
