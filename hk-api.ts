/* eslint-disable @typescript-eslint/no-unused-vars */
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import { openURL } from 'expo-linking';
import {
  HKChecklist,
  HKExtendedJob,
  HKJob,
  HKJobSection,
  HKSection,
  HKTask,
} from '~/types';
import { dummyJob, dummyItems, dummySections } from '~/hk-dummyData';

export const fetchItems = async (
  keeper: string,
  token: string,
): Promise<HKTask[]> => dummyItems;

export const fetchJob = async (
  jobId: string,
  keeper: string,
  token: string,
): Promise<HKExtendedJob> => {
  const res = await AsyncStorage.getItem(`job-${jobId}`);
  const job = res ? JSON.parse(res) : null;
  return job;
};

export const fetchSections = async (
  keeper: string,
  token: string,
): Promise<HKSection[]> => dummySections;

export const updateJobSection = async (
  jobSection: Omit<HKJobSection, 'completed_at'>,
  keeper: string,
  token: string,
) => {
  if (!keeper || !token) {
    return null;
  }

  const { jobId, section_id: sectionId } = jobSection;

  const job = await fetchJob(jobId, keeper, token);
  if (!job) return null;

  const updatedSections = job.jobSections.map(section =>
    section.section_id === sectionId ? { ...section, ...jobSection } : section,
  );

  const updatedJob: HKExtendedJob = {
    ...job,
    jobSections: updatedSections,
  };

  await AsyncStorage.setItem(`job-${jobId}`, JSON.stringify(updatedJob));

  return updatedJob;
};

export const addJob = async (
  job: HKJob,
  keeper: string,
  token: string,
): Promise<HKExtendedJob> => {
  const dummyExtendedJob = {
    ...dummyJob,
    ...job,
  };

  await AsyncStorage.setItem(
    `job-${dummyExtendedJob.jobId}`,
    JSON.stringify(dummyExtendedJob),
  );

  return dummyExtendedJob as HKExtendedJob;
};

const startNextChecklist = async (
  job: HKExtendedJob,
  checklist: HKChecklist,
  sections: HKSection[],
) => {
  if (!sections.length || job.jobSections.length === sections.length) {
    return job;
  }

  const nextSection = sections.find(
    section =>
      !job.jobSections.find(
        jobSection =>
          jobSection.section_id === section.id &&
          jobSection.checklist === checklist,
      ),
  );

  if (!nextSection) {
    return job;
  }

  const startedAt = new Date().toISOString();
  const formattedStartedAt = startedAt.replace('T', ' ').slice(0, -5);
  const updatedSections: HKJobSection[] = [
    {
      id: Math.random().toString(36).substring(2, 9),
      jobId: job.jobId as string,
      section_id: nextSection.id,
      checklist,
      // @ts-expect-error dummy data
      items: null,
      status: 'started',
      started_at: formattedStartedAt,
      // @ts-expect-error dummy data
      completed_at: null,
      location: null,
    },
  ];
  const updatedJob = {
    ...job,
    jobSections: [...job.jobSections, ...updatedSections],
  };

  await AsyncStorage.setItem(`job-${job.jobId}`, JSON.stringify(updatedJob));

  return updatedJob;
};

export const startChecklist = async (
  job: HKExtendedJob,
  checklist: HKChecklist,
  keeper: string,
  token: string,
): Promise<HKExtendedJob> => {
  if (!keeper || !token || !job.jobId) {
    return job;
  }
  const sections = await fetchSections(keeper, token);
  const updatedJob = await startNextChecklist(job, checklist, sections);

  return updatedJob;
};

export const completeJobSection = async (
  jobSection: Omit<HKJobSection, 'completed_at'>,
  keeper: string,
  token: string,
): Promise<HKExtendedJob> => {
  const currentJob = await fetchJob(jobSection.jobId, keeper, token);
  const sections = await fetchSections(keeper, token);

  const completedAt = new Date().toISOString();
  const formattedCompletedAt = completedAt.replace('T', ' ').slice(0, -5);

  const completedSection: HKJobSection = {
    ...jobSection,
    completed_at: formattedCompletedAt,
    status: 'completed',
  };

  const updatedJob = {
    ...currentJob,
    jobSections: [
      ...currentJob.jobSections.map(section =>
        section.id === jobSection.id ? completedSection : section,
      ),
    ],
  };

  await AsyncStorage.setItem(
    `job-${jobSection.jobId}`,
    JSON.stringify(updatedJob),
  );

  const nextJob = await startNextChecklist(
    updatedJob,
    jobSection.checklist,
    sections,
  );

  return nextJob;
};

const baseUrl = (
  task: string,
  subtask: string,
  keeper: string,
  token: string,
) =>
  `https://vacationrentalconnect.com/index.php?option=com_jomres&task=${task}&subtask=${subtask}&keeper=${keeper}&token=${token}`;

export const finishJob = async (
  jobId: string,
  keeper: string,
  token: string,
) => {
  const url = `${baseUrl(
    'housekeeping',
    'finishtask',
    keeper,
    token,
  )}&taskid=${jobId}`;

  if (Platform.OS === 'web') {
    window.location.assign(url);
  } else {
    openURL(url);
  }
};
